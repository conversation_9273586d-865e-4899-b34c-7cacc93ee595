<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1000"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content">
      <div layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="title">基本信息</div>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="时间维度" prop="planType">
                <a-radio-group
                  :disabled="form.planReportId !== undefined"
                  v-model="form.planType"
                  :options="radioOptions"
                  @change="handlePlanTypeChange"
                ></a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="计划名称" prop="planName">
                <a-input v-model="form.planName" allowClear placeholder="请输入" :disabled="true" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="上报单位" prop="districtCode">
                <a-input v-model="deptName" :disabled="true" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24" :span="12">
              <a-form-model-item label="计划时间" prop="dateRange">
                <a-range-picker
                  :disabled="form.planReportId !== undefined"
                  style="width: 100%"
                  v-model="form.dateRange"
                  :allowClear="true"
                  :format="form.planType === 3 ? 'YYYY-MM' : 'YYYY-MM-DD'"
                  :mode="form.planType === 3 ? ['month', 'month'] : ['date', 'date']"
                  :placeholder="['开始时间', '结束时间']"
                  :disabledDate="disabledDate"
                  @panelChange="handlePanelChange"
                  @change="handleDateRangeChange"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="title">用水计划</div>
            </a-col>
            <div style="margin: 0 16px 5px">
              <span style="font-weight: 500">注：</span>
              <span>1Q日=8.64万m³</span>
            </div>

            <VxeTable
              :key="columns.length"
              style="margin: 0 16px"
              :autoHeight="true"
              ref="vxeTableRef"
              :isShowTableHeader="false"
              :columns="columns"
              :tableData="tableData"
              :tablePage="false"
              :rowConfig="{ isHover: false }"
            />

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="备注" prop="remarks">
                <a-textarea
                  v-model="form.remarks"
                  placeholder="请输入"
                  allowClear
                  :disabled="form.planReportId !== undefined"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading" v-if="!rowInfo.isDetail">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOrgTree } from '@/api/user'
  import { addPlanReport, getPlanReportDetails, updatePlanReport } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable/index.vue'
  import getMapFlatTree from '@/utils/getMapFlatTree.js'

  export default {
    name: 'FormUseWater',
    components: { AntModal, VxeTable },
    props: ['radioOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        formTitle: '',
        deptName: undefined,

        rowInfo: {},

        columns: [],
        tableData: [],
        // 表单参数
        form: {
          planReportId: undefined,
          planType: 1,
          planName: undefined,
          dateRange: undefined,
          remarks: undefined,
        },
        rules: {
          planType: [{ required: true, message: '时间维度不能为空', trigger: 'change' }],
          planName: [{ required: true, message: '计划名称不能为空', trigger: 'blur' }],
          dateRange: [{ required: true, message: '计划时间不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      disabledDate(current) {
        return current && current < moment().endOf('day')
      },
      handleDateRangeChange() {
        setTimeout(() => {
          this.$nextTick(() => {
            this.form.planName =
              this.rowInfo?.planName ||
              `${this.deptName}${this.radioOptions.find(item => item.value === this.form.planType)?.label}计划${this.form.dateRange[0].format('YYYY-MM-DD')}`
            this.dealColumns()
          })
        }, 20)
      },
      handlePanelChange(value, mode) {
        this.form.dateRange = value
        this.handleDateRangeChange()
      },
      handlePlanTypeChange(e) {
        if (e.target.value === 1) {
          this.form.dateRange = [moment().add(1, 'day'), moment().add(5, 'day')]
        }
        if (e.target.value === 2) {
          this.form.dateRange = [moment().add(1, 'day'), moment().add(10, 'day')]
        }
        if (e.target.value === 3) {
          this.form.dateRange = [moment().add(1, 'month').startOf('month'), moment().add(1, 'month').endOf('month')]
        }

        if (this.form.planReportId) {
          this.form.dateRange = [moment(this.rowInfo.planStartDate), moment(this.rowInfo.planEndDate)]
        }

        this.form.planName =
          this.rowInfo?.planName ||
          `${this.deptName}${this.radioOptions.find(item => item.value === this.form.planType)?.label}计划${this.form.dateRange[0].format('YYYY-MM-DD')}`

        this.dealColumns()
      },
      dealColumns() {
        const dateRange = [moment(this.form.dateRange[0]), moment(this.form.dateRange[1])]

        const rowObj = {}
        new Array(dateRange[1].diff(dateRange[0], 'day') + 1).fill(0).forEach((item, index) => {
          const date = moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD')
          rowObj[date] = {
            planDate: date,
            planEndFlow: undefined,
            planStartFlow: undefined,
            planReportId: this.rowInfo?.planReportId,
          }
        })
        this.tableData = [rowObj]

        this.columns = [
          {
            title: '日期',
            align: 'center',
            width: 80,
            fixed: 'left',
            slots: {
              default: () => '计划流量',
            },
          },
          // 生成日期列
          ...[...Array(dateRange[1].diff(dateRange[0], 'day') + 1)].map((item, index) => {
            const date = moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD')

            return {
              title: moment(dateRange[0]).add(index, 'day').format('MM月DD日'),
              align: 'center',
              minWidth: 120,
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number
                        disabled={this.form.planReportId !== undefined}
                        size='small'
                        v-model={this.tableData[0][date].planStartFlow}
                        min={0}
                        precision={1}
                        onChange={val => {
                          this.$nextTick(() => {
                            if (val > this.tableData[0][date]?.planEndFlow) {
                              this.tableData[0][date].planEndFlow = val
                            }
                          })
                        }}
                      />
                      &nbsp;~&nbsp;
                      <a-input-number
                        disabled={this.form.planReportId !== undefined}
                        size='small'
                        v-model={this.tableData[0][date].planEndFlow}
                        min={0}
                        precision={1}
                        onChange={val => {
                          this.$nextTick(() => {
                            if (val < this.tableData[0][date]?.planStartFlow) {
                              this.tableData[0][date].planEndFlow = this.tableData[0][date].planStartFlow
                            }
                          })
                        }}
                      />
                    </div>
                  )
                },
              },
            }
          }),
        ]
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.open = true
        this.formTitle = '用水计划上报'

        getOrgTree().then(res => {
          const userDeptId = JSON.parse(localStorage.getItem('user')).deptId
          const flatObj = getMapFlatTree(res.data, 'deptId')

          this.deptName = flatObj?.[userDeptId]?.deptName

          this.handlePlanTypeChange({ target: { value: 1 } })
        })
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '用水计划上报'
        this.modalLoading = true
        this.rowInfo = { ...record }
        getPlanReportDetails({ planReportId: record.planReportId }).then(res => {
          this.form = {
            planReportId: res?.data?.planReportId,
            planType: +res?.data?.planType,
            planName: record?.planName || res?.data?.planName,
            dateRange: [moment(res?.data?.planStartDate), moment(res?.data?.planEndDate)],
            remarks: res?.data?.remarks,
          }

          getOrgTree().then(resp => {
            const userDeptId = JSON.parse(localStorage.getItem('user')).deptId
            const flatObj = getMapFlatTree(resp.data, 'deptId')

            this.deptName = record?.deptName || flatObj?.[userDeptId]?.deptName

            this.handlePlanTypeChange({ target: { value: this.form.planType } })

            const rowObj = {}
            res.data.reportDetailsVOs.forEach(item => {
              rowObj[item.planDate] = {
                planDate: item.planDate,
                planStartFlow: item.planStartFlow,
                planEndFlow: item.planEndFlow,
                reportDetailsId: item.reportDetailsId,
              }
            })
            this.tableData = [rowObj]
            this.modalLoading = false
          })
        })
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            const params = {
              ...this.form,
              planStartDate: this.form.dateRange[0].format('YYYY-MM-DD'),
              planEndDate: this.form.dateRange[1].format('YYYY-MM-DD'),
              reports: Object.values(this.tableData[0])
                .slice(0, -1)
                .map(item => {
                  if (item.planStartFlow === undefined && item.planEndFlow === undefined) {
                    return { ...item, planStartFlow: 0, planEndFlow: 0 }
                  }
                  if (item.planStartFlow === undefined || item.planEndFlow === undefined) {
                    if (item.planStartFlow === undefined) {
                      return { ...item, planStartFlow: item.planEndFlow }
                    }
                    if (item.planEndFlow === undefined) {
                      return { ...item, planEndFlow: item.planStartFlow }
                    }
                  }

                  return item
                }),
            }

            this.loading = true
            if (this.form.planReportId !== undefined) {
              updatePlanReport(params.reports)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                })
                .finally(() => (this.loading = false))
            } else {
              addPlanReport(params)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                })
                .finally(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  ::v-deep .table-cell-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .ant-input-number-handler-wrap {
      display: none;
    }
  }
</style>
