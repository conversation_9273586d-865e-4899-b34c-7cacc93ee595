<template>
  <!-- 新增修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1800"
    @cancel="cancel"
    modalHeight="800"
    @ok="handleSave"
    :show-ok-btn="!isView"
  >
    <div slot="content">
      <div class="public-water-volume-bill-report">
        <!-- 基本信息部分 -->
        <div class="basic-info-section">
          <div class="section-header" @click="toggleBasicInfoCollapse">
            <h3 class="section-title">
              <a-icon :type="basicInfoCollapsed ? 'right' : 'down'" style="margin-right: 8px;" />
              基本信息
            </h3>
          </div>

          <div v-show="!basicInfoCollapsed" class="section-content">
            <div class="form-row">
              <div class="form-item">
                <label>计划年份：</label>
                <a-date-picker
                  v-model="formData.planYear"
                  mode="year"
                  format="YYYY"
                  placeholder="请选择年份"
                  style="width: 200px"
                  :disabled="isEdit || isView"
                  :open="yearShowOne"
                  @openChange="openChangeOne"
                  @panelChange="panelChangeOne"
                />
              </div>
              <div class="form-item">
                <label>填报单位：</label>
                <a-select
                  v-model="formData.reportUnit"
                  placeholder="请选择填报单位"
                  style="width: 200px"
                  :disabled="isEdit || isView"
                >
                  <a-select-option
                    v-for="option in reportUnitOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </div>
            </div>
          </div>
        </div>

        <!-- 公管渠水量水费数据部分 -->
        <div class="water-volume-section">
          <!-- <h3 class="section-title">公管渠水量水费数据</h3> -->

          <!-- 遍历渲染多个水量水费表格组件 -->
          <WaterVolumeTable
            v-for="(tableConfig, index) in waterVolumeTableConfigs"
            :key="index"
            :title="tableConfig.title"
            :table-data="tableConfig.data"
            :table-ref="`waterVolumeTableRef_${index}`"
            :readonly="isView"
            :seasons="dynamicSeasons"
            :conversion-rate-value="conversionRates[tableConfig.title] || 0"
            @table-data-change="handleTableDataChange(index, $event)"
            @conversion-rate-change="handleConversionRateChange"
            @row-change="handleRowChange"
          />
        </div>

      </div>
    </div>
  </ant-modal>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import WaterVolumeTable from './WaterVolumeTable.vue'
  import moment from 'moment'
  import { addPublicWaterVolumeBill, getPublicWaterVolumeBillById, updatePublicWaterVolumeBill, getAllIrrigationRounds } from '../service'
  import { getOrgTree } from '@/api/user'
  import { getProjectPage } from '@/api/common'
  import user from '@/store/modules/user'

  export default {
    name: 'FormDrawer',
    components: {
      AntModal,
      WaterVolumeTable
    },
    data() {
      return {
        open: false,
        modalLoading: false,
        formTitle: '',
        isEdit: false,
        isView: false,
        editId: null,
        yearShowOne: false, // 年份选择器显示状态
        basicInfoCollapsed: false, // 基本信息折叠状态
        formData: {
          planYear: moment(), // 默认为当前年份
          reportUnit: null
        },
        // 部门相关数据
        orgTree: [],
        reportUnitOptions: [],
        // 水量水费表格配置
        waterVolumeTableConfigs: [
          { title: '支3级别水费、水量', data: [] },
          { title: '支2级别水费、水量', data: [] },
          { title: '支1级别水费、水量', data: [] },
          { title: '斗3级别水费、水量', data: [] },
          { title: '斗2级别水费、水量', data: [] },
          { title: '斗1级别水费、水量', data: [] },
          { title: '农3级别水费、水量', data: [] },
          { title: '农2级别水费、水量', data: [] },
          { title: '农1级别水费、水量', data: [] },
          { title: '毛3级别水费、水量', data: [] },
          { title: '毛2级别水费、水量', data: [] },
          { title: '毛1级别水费、水量', data: [] }
        ],
        // 折算率数据
        conversionRates: {},
        // 灌溉轮次数据
        irrigationRounds: [],
        // 动态季度配置
        dynamicSeasons: [],
        // 工程信息数据
        projectOptions: []
      }
    },
    created() {
      this.getOrgTreeData()
      this.loadProjectOptions()
    },
    methods: {
      cancel() {
        this.open = false
        this.resetData()
      },

      async handleAdd() {
        this.open = true
        this.formTitle = '新增'
        this.isEdit = false
        this.editId = null

        // 确保组织机构数据已加载后再初始化
        if (this.reportUnitOptions.length > 0) {
          await this.initData()
        } else {
          // 如果组织机构数据还没加载，等待加载完成后再初始化
          const checkAndInit = async () => {
            if (this.reportUnitOptions.length > 0) {
              await this.initData()
            } else {
              setTimeout(checkAndInit, 100)
            }
          }
          await checkAndInit()
        }
      },

      async handleUpdate(record) {
        this.open = true
        this.formTitle = '编辑'
        this.isEdit = true
        this.editId = record.id
        this.modalLoading = true

        try {
          await this.loadEditData(record.id)
        } catch (error) {
          console.error('加载编辑数据失败:', error)
          this.$message.error('加载编辑数据失败')
        } finally {
          this.modalLoading = false
        }
      },

      async handleView(record) {
        this.open = true
        this.formTitle = '详情'
        this.isEdit = false
        this.isView = true
        this.editId = record.id
        this.modalLoading = true

        try {
          await this.loadEditData(record.id)
        } catch (error) {
          console.error('加载详情数据失败:', error)
          this.$message.error('加载详情数据失败')
        } finally {
          this.modalLoading = false
        }
      },

      // 重置数据
      resetData() {
        this.formData = {
          planYear: moment(), // 默认为当前年份
          reportUnit: null
        }
        // 重置所有表格数据
        this.waterVolumeTableConfigs.forEach(config => {
          config.data = []
        })
        this.conversionRates = {}
        this.isEdit = false
        this.isView = false
        this.editId = null
      },

      // 初始化数据（新增时使用）
      async initData() {
        this.formData.planYear = moment() // 默认为当前年份

        // 如果填报单位选项已加载，默认选择第一项
        if (this.reportUnitOptions.length > 0) {
          this.formData.reportUnit = this.reportUnitOptions[0].value
        }

        // 初始化水量水费表格数据
        this.waterVolumeTableConfigs.forEach(config => {
          config.data = []
        })
        this.conversionRates = {}

        // 加载灌溉轮次数据
        await this.loadIrrigationRounds()
      },
      // 年份选择器打开状态变化
      openChangeOne(status) {
        this.yearShowOne = status
      },

      // 年份选择器面板变化
      panelChangeOne(value) {
        this.formData.planYear = moment(value)
        this.yearShowOne = false
        // 年份变化时重新获取灌溉轮次数据
        this.loadIrrigationRounds()
      },

      // 切换基本信息折叠状态
      toggleBasicInfoCollapse() {
        this.basicInfoCollapsed = !this.basicInfoCollapsed
      },

      // 加载灌溉轮次数据
      async loadIrrigationRounds() {
        if (!this.formData.planYear) {
          return
        }

        try {
          const fillYear = this.formData.planYear.year()
          console.log('获取灌溉轮次数据，年份:', fillYear)

          const responses = await getAllIrrigationRounds(fillYear)
          this.irrigationRounds = []
          this.dynamicSeasons = []

          responses.forEach((response, index) => {
            if (response.success && response.data) {
              const roundData = {
                round: index + 1,
                ssStart: response.data.ssStart,
                ssEnd: response.data.ssEnd
              }
              this.irrigationRounds.push(roundData)

              // 生成动态季度配置
              const seasonConfig = this.generateSeasonConfig(roundData)
              if (seasonConfig) {
                this.dynamicSeasons.push(seasonConfig)
              }
            }
          })

          console.log('灌溉轮次数据:', this.irrigationRounds)
          console.log('动态季度配置:', this.dynamicSeasons)

        } catch (error) {
          console.error('获取灌溉轮次数据失败:', error)
          this.$message.error('获取灌溉轮次数据失败')
        }
      },

      // 生成季度配置
      generateSeasonConfig(roundData) {
        const startDate = moment(roundData.ssStart)
        const endDate = moment(roundData.ssEnd)

        if (!startDate.isValid() || !endDate.isValid()) {
          return null
        }

        const months = []
        let current = startDate.clone().startOf('month')

        while (current.isSameOrBefore(endDate, 'month')) {
          months.push(current.month() + 1) // moment的月份是0-11，需要+1
          current.add(1, 'month')
        }

        if (months.length === 0) {
          return null
        }

        return {
          key: `round${roundData.round}`,
          title: `${startDate.format('M')}月-${endDate.format('M')}月`,
          months: months,
          round: roundData.round
        }
      },

      // 加载编辑数据
      async loadEditData(id) {
        try {
          // 调用详情接口
          const response = await getPublicWaterVolumeBillById(id)
          if (response.success && response.data) {
            const data = response.data

            // 设置基本信息
            this.formData = {
              planYear: moment(data.fillYear, 'YYYY'),
              reportUnit: data.depId
            }

            // 先加载灌溉轮次数据，确保动态季度配置正确
            await this.loadIrrigationRounds()

            // 确保工程信息数据已加载
            if (this.projectOptions.length === 0) {
              await this.loadProjectOptions()
            }

            // 解析详情数据并填充到表格中
            this.parseDetailDataToTables(data.detailList)

          } else {
            this.$message.error(response.message || '加载数据失败')
          }
        } catch (error) {
          console.error('加载编辑数据失败:', error)
          throw error
        }
      },

      // 解析详情数据到表格
      parseDetailDataToTables(detailList) {
        if (!detailList || detailList.length === 0) {
          return
        }

        // 清空现有数据
        this.waterVolumeTableConfigs.forEach(config => {
          config.data = []
        })
        this.conversionRates = {}

        // 按渠道编号和级别分组，合并不同灌溉轮次的数据
        const channelGroups = {}

        detailList.forEach(detail => {
          const tableKey = this.getTableKeyFromChannelType(detail.channelType, detail.channelTypeSn)
          const channelKey = `${tableKey}_${detail.channelNo}`

          if (!channelGroups[channelKey]) {
            channelGroups[channelKey] = {
              tableKey: tableKey,
              channelData: {
                id: detail.id,
                projectId: this.getProjectId(detail.channelNo),
                channelName: this.getChannelName(detail.channelNo),
                channelCode: detail.channelNo,
                waterFeePrice20: detail.feeLeTwenty || 0,
                waterFeePrice50: detail.feeGeTwenty || 0
              },
              conversionRate: detail.conversionRate,
              irrigationRounds: {}
            }

            // 初始化所有季度数据为0
            this.dynamicSeasons.forEach(season => {
              channelGroups[channelKey].channelData[`${season.key}_contractWater`] = 0
              season.months.forEach(month => {
                channelGroups[channelKey].channelData[`${season.key}_month${month}`] = 0
              })
              channelGroups[channelKey].channelData[`${season.key}_diversifiedWater`] = 0
            })
          }

          // 找到对应的季度
          const season = this.dynamicSeasons.find(s => s.round === detail.irrigationRound)
          if (season) {
            // 填充包干水量
            if (detail.contractedVolume > 0) {
              channelGroups[channelKey].channelData[`${season.key}_contractWater`] = detail.contractedVolume
            }

            // 填充多元化供水
            if (detail.multipleSupply > 0) {
              channelGroups[channelKey].channelData[`${season.key}_diversifiedWater`] = detail.multipleSupply
            }

            // 填充实际使用水量
            if (detail.actualUsageDetailList && detail.actualUsageDetailList.length > 0) {
              detail.actualUsageDetailList.forEach(usage => {
                if (season.months.includes(usage.useMonth)) {
                  channelGroups[channelKey].channelData[`${season.key}_month${usage.useMonth}`] = usage.actualUsage
                }
              })
            }
          }
        })

        // 按表格分组并填充数据
        const tableGroups = {}
        Object.values(channelGroups).forEach(group => {
          if (!tableGroups[group.tableKey]) {
            tableGroups[group.tableKey] = {
              channels: [],
              conversionRate: group.conversionRate
            }
          }

          // 为每行数据添加conversionRate字段
          group.channelData.conversionRate = group.conversionRate
          tableGroups[group.tableKey].channels.push(group.channelData)
        })

        // 将分组数据填充到对应的表格配置中
        Object.keys(tableGroups).forEach(tableKey => {
          const group = tableGroups[tableKey]
          const config = this.waterVolumeTableConfigs.find(c => c.title === tableKey)

          if (config) {
            config.data = group.channels
            this.conversionRates[tableKey] = group.conversionRate
          }
        })

        console.log('解析后的表格数据:', this.waterVolumeTableConfigs)
        console.log('解析后的折算率:', this.conversionRates)
      },

      // 根据渠道类型和序号获取表格标题
      getTableKeyFromChannelType(channelType, channelTypeSn) {
        const typeMap = {
          1: '支',
          2: '斗',
          3: '农',
          4: '毛'
        }

        const typeName = typeMap[channelType] || '支'
        return `${typeName}${channelTypeSn}级别水费、水量`
      },
      // 获取组织机构树数据
      async getOrgTreeData() {
        try {
          const response = await getOrgTree()
          if (response.success && response.data) {
            this.orgTree = response.data
            this.buildReportUnitOptions()
          }
        } catch (error) {
          console.error('获取组织机构树失败:', error)
        }
      },
      // 构建填报单位选项
      buildReportUnitOptions() {
        const options = []
        // 优先使用loginOrgId，其次使用deptId，最后使用默认值
        const currentUserDepId = this.$store.state.user.loginOrgId || this.$store.state.user.deptId || 10020

        console.log('当前用户部门ID:', currentUserDepId)
        console.log('用户store信息:', this.$store.state.user)

        // 递归查找匹配的部门
        const findMatchingDept = (nodes, targetDepId) => {
          for (const node of nodes) {
            // 确保ID比较时类型一致
            if (Number(node.deptId) === Number(targetDepId)) {
              return node
            }
            if (node.children && node.children.length > 0) {
              const found = findMatchingDept(node.children, targetDepId)
              if (found) return found
            }
          }
          return null
        }

        const matchedDept = findMatchingDept(this.orgTree, currentUserDepId)

        console.log('匹配到的部门:', matchedDept)

        if (matchedDept) {
          // 添加当前部门
          options.push({
            value: matchedDept.deptId,
            label: matchedDept.deptName,
            deptId: matchedDept.deptId
          })

          // 如果有子部门，也添加子部门
          if (matchedDept.children && matchedDept.children.length > 0) {
            matchedDept.children.forEach(child => {
              options.push({
                value: child.deptId,
                label: child.deptName,
                deptId: child.deptId
              })
            })
          }
        }

        this.reportUnitOptions = options
        console.log('构建的填报单位选项:', this.reportUnitOptions)

        // 如果是新增模式且没有设置填报单位，默认选择第一项
        if (!this.isEdit && !this.formData.reportUnit && options.length > 0) {
          this.formData.reportUnit = options[0].value
          console.log('默认选择第一个填报单位:', options[0])
        }
      },

      // 表格数据变化处理
      handleTableDataChange(tableIndex, tableData) {
        if (this.waterVolumeTableConfigs[tableIndex]) {
          this.waterVolumeTableConfigs[tableIndex].data = tableData
        }
      },

      // 折算率变化处理
      handleConversionRateChange({ title, rate }) {
        console.log('接收到折算率变化事件:', { title, rate })
        this.conversionRates[title] = rate

        // 更新该级别下所有渠道数据的conversionRate字段
        const config = this.waterVolumeTableConfigs.find(c => c.title === title)
        if (config && config.data) {
          config.data.forEach(row => {
            // 为每行数据添加或更新conversionRate字段
            this.$set(row, 'conversionRate', rate)
          })
        }

        console.log(`更新${title}的折算率为${rate}，影响${config?.data?.length || 0}条渠道数据`)
        console.log('当前所有折算率:', this.conversionRates)
      },

      // 行数据变化处理
      handleRowChange({ row, field, title }) {
        console.log('行数据变化:', { row, field, title })
        // 这里可以添加数据联动计算逻辑
      },

      // 校验水量水费表格数据
      validateWaterVolumeData() {
        console.log('开始校验水量水费表格数据')
        console.log('当前折算率:', this.conversionRates)

        // 遍历所有水量水费表格配置
        for (const config of this.waterVolumeTableConfigs) {
          const title = config.title
          const conversionRate = this.conversionRates[title] || 0
          const tableData = config.data || []

          console.log(`校验 ${title}: 折算率=${conversionRate}, 表格数据长度=${tableData.length}`)

          // 如果折算率不为0，但表格没有数据，则校验失败
          if (conversionRate > 0) {
            // 检查是否有渠道数据
            if (tableData.length === 0) {
              console.log(`校验失败: ${title} 折算率不为0但没有渠道数据`)
              return {
                isValid: false,
                message: `${title}：折算率不为0时，需要选择渠道填写数据`
              }
            }

            // 检查是否有实际的水量数据
            const hasValidData = tableData.some(row => this.checkRowHasValidData(row))
            console.log(`${title} 是否有有效数据:`, hasValidData)

            if (!hasValidData) {
              console.log(`校验失败: ${title} 折算率不为0但没有有效的水量数据`)
              return {
                isValid: false,
                message: `${title}：折算率不为0时，需要选择渠道填写数据`
              }
            }
          }
        }

        console.log('水量水费表格数据校验通过')
        return { isValid: true }
      },

      // 检查行是否有有效数据（复用WaterVolumeTable组件的逻辑）
      checkRowHasValidData(row) {
        if (!row) return false

        // 检查是否有包干水量、实用水量或多元化供水数据
        let hasData = false

        // 检查所有季度的数据
        this.dynamicSeasons.forEach(season => {
          // 检查包干水量
          if (row[`${season.key}_contractWater`] && row[`${season.key}_contractWater`] > 0) {
            hasData = true
          }

          // 检查月份实用水量
          season.months.forEach(month => {
            if (row[`${season.key}_month${month}`] && row[`${season.key}_month${month}`] > 0) {
              hasData = true
            }
          })

          // 检查多元化供水
          if (row[`${season.key}_diversifiedWater`] && row[`${season.key}_diversifiedWater`] > 0) {
            hasData = true
          }
        })

        return hasData
      },
      // 保存
      async handleSave() {
        // 详情模式下不允许保存
        if (this.isView) {
          return
        }

        try {
          // 基本信息验证
          if (!this.formData.planYear) {
            this.$message.error('请选择计划年份')
            return
          }
          if (!this.formData.reportUnit) {
            this.$message.error('请选择填报单位')
            return
          }

          // 水量水费表格数据校验
          const validationResult = this.validateWaterVolumeData()
          if (!validationResult.isValid) {
            this.$message.error(validationResult.message)
            return
          }

          this.modalLoading = true

          // 根据是否编辑模式构建不同格式的请求数据
          const requestData = this.isEdit
            ? this.buildEditRequestData()
            : this.buildRequestData()

          console.log('保存数据:', requestData)

          // 真实接口调用
          const response = this.isEdit
            ? await updatePublicWaterVolumeBill(requestData)
            : await addPublicWaterVolumeBill(requestData)
          
          if (response.success) {
            this.$message.success(this.isEdit ? '更新成功' : '新增成功')
            this.open = false
            this.$emit('ok')
          } else {
            this.$message.error(response.message || '保存失败')
          }
        } catch (error) {
          console.error('保存失败:', error)
          this.$message.error('保存失败')
        } finally {
          this.modalLoading = false
        }
      },

      // 构建请求数据
      buildRequestData() {
        const requestData = {
          depId: this.formData.reportUnit || user.state.deptId,
          fillYear: this.formData.planYear.year(),
          detailList: []
        }

        // 如果是编辑模式，添加ID
        if (this.isEdit && this.editId) {
          requestData.id = this.editId
        }

        // 按灌溉轮次组织数据
        // 遍历每个灌溉轮次
        this.dynamicSeasons.forEach(season => {
          // 遍历每个表格配置（不同级别的渠道）
          this.waterVolumeTableConfigs.forEach((config) => {
            const channelType = this.getChannelTypeFromTitle(config.title)
            const channelTypeSn = this.getChannelTypeSnFromTitle(config.title)

            if (config.data && config.data.length > 0) {
              config.data.forEach(row => {
                // 检查该行在当前季度是否有数据
                const hasSeasonData = this.checkRowHasSeasonData(row, season)

                if (hasSeasonData) {
                  // 获取该级别的折算率
                  const conversionRate = this.conversionRates[config.title]

                  const detailItem = {
                    channelNo: row.channelCode || '',
                    channelType: channelType,
                    channelTypeSn: channelTypeSn,
                    irrigationRound: season.round,
                    contractedVolume: row[`${season.key}_contractWater`] || 0,
                    conversionRate: conversionRate || 0,
                    feeGeTwenty: row.waterFeePrice50 || 0,
                    feeLeTwenty: row.waterFeePrice20 || 0,
                    multipleSupply: row[`${season.key}_diversifiedWater`] || 0,
                    actualUsageDetailList: this.buildSeasonActualUsageDetailList(row, season)
                  }

                  requestData.detailList.push(detailItem)
                }
              })
            }
          })
        })

        return requestData
      },

      // 从标题获取渠道类型
      getChannelTypeFromTitle(title) {
        if (title.includes('支')) return 1
        if (title.includes('斗')) return 2
        if (title.includes('农')) return 3
        if (title.includes('毛')) return 4
        return 1 // 默认值
      },

      // 从标题获取渠道类型序号
      getChannelTypeSnFromTitle(title) {
        const match = title.match(/(\d+)级别/)
        return match ? parseInt(match[1]) : 1
      },

      // 检查行是否有数据
      checkRowHasData(row) {
        // 检查是否有包干水量、实用水量或多元化供水数据
        let hasData = false

        // 检查所有季度的数据
        this.dynamicSeasons.forEach(season => {
          // 检查包干水量
          if (row[`${season.key}_contractWater`] && row[`${season.key}_contractWater`] > 0) {
            hasData = true
          }

          // 检查月份实用水量
          season.months.forEach(month => {
            if (row[`${season.key}_month${month}`] && row[`${season.key}_month${month}`] > 0) {
              hasData = true
            }
          })

          // 检查多元化供水
          if (row[`${season.key}_diversifiedWater`] && row[`${season.key}_diversifiedWater`] > 0) {
            hasData = true
          }
        })

        return hasData
      },

      // 检查行在特定季度是否有数据
      checkRowHasSeasonData(row, season) {
        if (!row || !season) return false

        // 检查包干水量
        if (row[`${season.key}_contractWater`] && row[`${season.key}_contractWater`] > 0) {
          return true
        }

        // 检查月份实用水量
        for (let month of season.months) {
          if (row[`${season.key}_month${month}`] && row[`${season.key}_month${month}`] > 0) {
            return true
          }
        }

        // 检查多元化供水
        if (row[`${season.key}_diversifiedWater`] && row[`${season.key}_diversifiedWater`] > 0) {
          return true
        }

        return false
      },

      // 计算总包干水量
      calculateTotalContractedVolume(row) {
        let total = 0
        this.dynamicSeasons.forEach(season => {
          const value = row[`${season.key}_contractWater`] || 0
          total += Number(value)
        })
        return total
      },

      // 计算总多元化供水
      calculateTotalMultipleSupply(row) {
        let total = 0
        this.dynamicSeasons.forEach(season => {
          const value = row[`${season.key}_diversifiedWater`] || 0
          total += Number(value)
        })
        return total
      },

      // 构建实际使用水量详情列表（旧方法，保留用于其他地方）
      buildActualUsageDetailList(row) {
        const actualUsageDetailList = []

        this.dynamicSeasons.forEach(season => {
          season.months.forEach(month => {
            const actualUsage = row[`${season.key}_month${month}`] || 0
            if (actualUsage > 0) {
              actualUsageDetailList.push({
                actualUsage: Number(actualUsage),
                irrigationRound: season.round,
                useMonth: month
              })
            }
          })
        })

        return actualUsageDetailList
      },

      // 构建特定季度的实际使用水量详情列表
      buildSeasonActualUsageDetailList(row, season) {
        const actualUsageDetailList = []

        season.months.forEach(month => {
          const actualUsage = row[`${season.key}_month${month}`] || 0
          if (actualUsage > 0) {
            actualUsageDetailList.push({
              actualUsage: Number(actualUsage),
              useMonth: month
            })
          }
        })

        return actualUsageDetailList
      },

      // 构建编辑请求数据（使用与新增相同的格式）
      buildEditRequestData() {
        // 编辑时使用与新增相同的数据结构，只是添加ID字段
        const requestData = this.buildRequestData()

        // 添加编辑ID
        if (this.editId) {
          requestData.id = this.editId
        }

        console.log('编辑请求数据:', requestData)
        return requestData
      },

      // 加载工程信息数据
      async loadProjectOptions() {
        try {
          const params = {
            pageNum: 1,
            pageSize: 1000,
            districtCode: "0"
          }

          const response = await getProjectPage(params)
          if (response.code === 200 && response.data && response.data.data) {
            this.projectOptions = response.data.data
            console.log('加载工程信息数据成功:', this.projectOptions.length, '条')
          } else {
            console.warn('加载工程信息数据失败:', response.message)
          }
        } catch (error) {
          console.error('加载工程信息数据失败:', error)
        }
      },

      // 获取渠道名称
      getChannelName(channelNo) {
        if (this.projectOptions.length === 0) {
          console.warn('项目数据未加载，返回原始渠道编码')
          return channelNo
        }

        const project = this.projectOptions.find(option => option.projectCode === channelNo)
        return project ? project.projectName : channelNo
      },

      // 获取项目ID
      getProjectId(channelNo) {
        if (this.projectOptions.length === 0) {
          console.warn('项目数据未加载，返回渠道编码作为ID')
          return channelNo
        }

        const project = this.projectOptions.find(option => option.projectCode === channelNo)
        return project ? project.projectId : channelNo
      }
    }
  }
</script>

<style lang="less" scoped>
.public-water-volume-bill-report {
  background: #fff;

  .basic-info-section {
    margin-bottom: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;

    .section-header {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #e8e8e8;
      cursor: pointer;
      user-select: none;

      &:hover {
        background: #f0f0f0;
      }

      .section-title {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
        display: flex;
        align-items: center;
      }
    }

    .section-content {
      padding: 16px;

      .form-row {
        display: flex;
        gap: 30px;

        .form-item {
          display: flex;
          align-items: center;

          label {
            margin-right: 8px;
            font-weight: 500;
            color: #333;
          }
        }
      }
    }
  }

  .water-volume-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .table-placeholder {
      background: #f9f9f9;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      padding: 40px;
      text-align: center;
      color: #999;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;

      p {
        margin: 5px 0;
        font-size: 14px;
      }
    }
  }
}
</style>
